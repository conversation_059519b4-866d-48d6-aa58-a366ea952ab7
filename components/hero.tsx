import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Heart, 
  Shield, 
  Zap, 
  PawPrint, 
  Sparkles,
  ArrowRight,
  CheckCircle
} from "lucide-react";

export function Hero() {
  return (
    <div className="flex flex-col gap-16 items-center">
      {/* Main Hero Content */}
      <div className="text-center space-y-8 max-w-4xl">
        <div className="space-y-4">
          <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
            <Sparkles className="w-4 h-4 mr-2" />
            AI-Powered Pet Care
          </Badge>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
            Your Pet&apos;s{" "}
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI Companion
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Revolutionary AI technology that understands, cares for, and enhances your pet&apos;s life. 
            From health monitoring to behavioral insights, we&apos;ve got your furry friend covered.
          </p>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button size="lg" className="text-lg px-8 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
            Get Started Free
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
          <Button variant="outline" size="lg" className="text-lg px-8 py-6">
            Watch Demo
          </Button>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full max-w-6xl">
        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-200">
          <CardContent className="p-6 text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Brain className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold">Smart Health Monitoring</h3>
            <p className="text-muted-foreground text-sm">
              AI-powered health tracking with predictive analytics for early disease detection
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-purple-200">
          <CardContent className="p-6 text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Heart className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold">Behavioral Insights</h3>
            <p className="text-muted-foreground text-sm">
              Understand your pet&apos;s emotions and behavior patterns with advanced AI analysis
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-green-200">
          <CardContent className="p-6 text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Shield className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold">24/7 Safety</h3>
            <p className="text-muted-foreground text-sm">
              Round-the-clock monitoring and instant alerts for your pet&apos;s safety and wellbeing
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-orange-200">
          <CardContent className="p-6 text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-orange-100 to-orange-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Zap className="w-8 h-8 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold">Instant Recommendations</h3>
            <p className="text-muted-foreground text-sm">
              Get personalized care recommendations and training tips powered by AI
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Trust Indicators */}
      <div className="text-center space-y-6">
        <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>Trusted by 50,000+ pet owners</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>Veterinarian approved</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>99.9% uptime guarantee</span>
          </div>
        </div>
        
        <div className="flex justify-center items-center gap-2 text-2xl">
          <PawPrint className="w-6 h-6 text-blue-600" />
          <span className="font-semibold">PawHub</span>
          <span className="text-muted-foreground">- Where AI meets pet care</span>
        </div>
      </div>

      <div className="w-full p-[1px] bg-gradient-to-r from-transparent via-foreground/10 to-transparent my-8" />
    </div>
  );
}
