import Image from "next/image";
import Link from "next/link";

export function Footer() {
  return (
    <footer className="relative w-full mt-24 text-white">
      <div
        className="absolute inset-0 -z-10 bg-[url('/footer-bg.svg')] bg-cover bg-no-repeat bg-top"
        aria-hidden
      />
      <Image className="absolute top-12 lg:top-0 right-4 lg:right-32 w-40 h-auto lg:w-auto" src="/footer-mascot.png" alt="Footer Background" width={338} height={167} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          <div>
            <div className="mb-6">
              <Image src="/paw-hub-solid.svg" alt="Paw Hub" width={120} height={70} />
            </div>
            <p className="font-semibold">K&P Investment Trading and Services Co,LTD</p>
            <p className="mt-3">Địa chỉ: abc</p>
            <p>GCN ĐKKD số: 0123456789</p>
            <p className="mt-6 font-semibold">Mạng xã hội PawHub</p>
            <p className="mt-3">Số giấy phép thiết lập MXH:</p>
            <p>Người chịu trách nhiệm:</p>
          </div>

          <div className="grid grid-cols-2 gap-6 text-white/90">
            <ul className="space-y-3">
              <li><Link href="#" className="hover:underline">About us</Link></li>
              <li><Link href="#" className="hover:underline">Vision & Mission</Link></li>
              <li><Link href="#" className="hover:underline">Contact</Link></li>
              <li><Link href="#" className="hover:underline">Terms & Condition</Link></li>
              <li><Link href="#" className="hover:underline">Privacy Policy</Link></li>
            </ul>

            <div>
              <p className="font-semibold mb-3">Follow us</p>
              <div className="flex items-center gap-4">
                <Link aria-label="Facebook" href="#" className="h-10 w-10 rounded-full bg-white/10 hover:bg-white/20 grid place-items-center">
                  <span className="sr-only">Facebook</span>
                  {/* Simple FB glyph */}
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M22 12.06C22 6.5 17.52 2 12 2S2 6.5 2 12.06c0 5.01 3.66 9.17 8.44 9.94v-7.03H7.9v-2.91h2.54V9.41c0-2.5 1.49-3.88 3.77-3.88 1.09 0 2.23.2 2.23.2v2.45h-1.26c-1.24 0-1.63.77-1.63 1.56v1.86h2.78l-.44 2.91h-2.34v7.03C18.34 21.23 22 17.07 22 12.06z" /></svg>
                </Link>
                <Link aria-label="YouTube" href="#" className="h-10 w-10 rounded-full bg-white/10 hover:bg-white/20 grid place-items-center">
                  <span className="sr-only">YouTube</span>
                  <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor"><path d="M23.5 6.2a3 3 0 0 0-2.1-2.1C19.5 3.5 12 3.5 12 3.5s-7.5 0-9.4.6A3 3 0 0 0 .5 6.2 31 31 0 0 0 0 12a31 31 0 0 0 .5 5.8 3 3 0 0 0 2.1 2.1c1.9.6 9.4.6 9.4.6s7.5 0 9.4-.6a3 3 0 0 0 2.1-2.1A31 31 0 0 0 24 12a31 31 0 0 0-.5-5.8zM9.6 15.5V8.5L15.8 12l-6.2 3.5z" /></svg>
                </Link>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 lg:justify-end">
            <Image src="/bo-cong-thuong-dang-ky.png" alt="Bộ Công Thương Đã đăng ký" width={140} height={44} />
            <Image src="/bo-cong-thuong-thong-bao.png" alt="Bộ Công Thương Đã thông báo" width={140} height={44} />
          </div>
        </div>

        <div className="mt-10 border-t border-white/30 pt-6 text-sm text-white/80">
          Copyright ©2025 by K&P. All rights reserved.
        </div>
      </div>
    </footer>
  );
}

export default Footer;

