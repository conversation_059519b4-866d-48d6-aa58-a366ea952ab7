"use client";

import { Button } from "@/components/ui/button";
import { useState } from "react";

export function LogoutButton() {
  const [isLoading, setIsLoading] = useState(false);

  const logout = async () => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Force a full page reload to ensure server components refresh
        // This is similar to how login handles session updates
        window.location.href = "/";
      } else {
        console.error('Logout failed');
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return <Button onClick={logout} disabled={isLoading}>
    {isLoading ? "Logging out..." : "Logout"}
  </Button>;
}
