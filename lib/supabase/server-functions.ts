import { createClient } from '@/lib/supabase/server';

/**
 * Retrieves a user's ID by their email address using a database function.
 * @param {string} email - The email address to look up.
 * @returns {Promise<string | null>} A Promise that resolves to the user's ID, or null if not found.
 */
export async function getUserIdByEmail(email: string): Promise<string | null> {
    try {
        const supabase = await createClient();
        console.log('Checking email:', email);
        
        const { data, error } = await supabase.rpc('get_user_id_by_email', { p_email: email });

        if (error) {
            console.error('getUserIdByEmail error:', error);
            return null;
        }

        console.log('getUserIdByEmail result:', data);
        return data;
    } catch (error) {
        console.error('getUserIdByEmail exception:', error);
        return null;
    }
}

/**
 * Checks if a user exists by their email address.
 * @param {string} email - The email address to check.
 * @returns {Promise<boolean>} A Promise that resolves to true if the user exists, false otherwise.
 */
export async function checkUserExistsByEmail(email: string): Promise<boolean> {
    try {
        const userId = await getUserIdByEmail(email);
        return userId !== null;
    } catch (error) {
        console.error('checkUserExistsByEmail error:', error);
        // Fallback: return false to allow signup attempt
        // The signup will fail with proper error if email exists
        return false;
    }
}
