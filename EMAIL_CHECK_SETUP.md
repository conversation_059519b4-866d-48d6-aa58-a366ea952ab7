# Email Existence Check Setup

This document explains how to set up the email existence check functionality using a database function.

## Database Setup

### 1. Run the Migration

Execute the SQL migration file to create the required database function:

```sql
-- Run this in your Supabase SQL editor or via migration
-- File: supabase/migrations/001_create_get_user_id_by_email_function.sql

-- Function to get user ID by email
create or replace function public.get_user_id_by_email(p_email text)
returns uuid
language plpgsql
security definer
as $$
begin
    return (
        select id
        from auth.users
        where email = p_email
        limit 1
    );
end;
$$;

-- Revoke all permissions from authenticated, anon, and public
revoke all on function public.get_user_id_by_email(text) from authenticated, anon, public;

-- Grant execute permission only to supabase_auth_admin
grant execute on function public.get_user_id_by_email(text) to supabase_auth_admin;
```

### 2. Verify Function Creation

You can verify the function was created by running:

```sql
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'get_user_id_by_email';
```

## How It Works

### 1. Client-Side Flow
- User fills out signup form and clicks "Sign Up"
- Form calls `/api/check-email` endpoint
- API endpoint calls `checkUserExistsByEmail()` server function
- Server function calls `getUserIdByEmail()` database function
- Response indicates if email exists or not

### 2. Security
- Database function is secured with `security definer`
- Only `supabase_auth_admin` can execute the function
- Client cannot directly access the `auth.users` table
- All access goes through the secure server-side API

### 3. Performance
- Direct database query is much faster than dummy password attempts
- No unnecessary authentication attempts
- Cleaner error handling
- Better user experience

## Files Created

1. **Database Migration**: `supabase/migrations/001_create_get_user_id_by_email_function.sql`
2. **Server Functions**: `lib/supabase/server-functions.ts`
3. **API Route**: `app/api/check-email/route.ts`
4. **Updated Form**: `components/login-form.tsx`

## Usage

The email check now happens automatically when users click "Sign Up". If the email already exists, they'll see:

> "This email is already registered. Please switch to Login mode or use a different email."

With a quick "Switch to Login mode →" button for easy mode switching.

## Benefits

- ✅ **Secure**: Uses proper database functions with restricted permissions
- ✅ **Fast**: Direct database query instead of authentication attempts
- ✅ **Clean**: No dummy password attempts or complex error parsing
- ✅ **Reliable**: Consistent behavior across different scenarios
- ✅ **Maintainable**: Clear separation of concerns with proper API structure
