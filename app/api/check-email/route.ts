import { NextRequest, NextResponse } from 'next/server';
import { checkUserExistsByEmail } from '@/lib/supabase/server-functions';

export async function POST(request: NextRequest) {
    try {
        console.log('API: check-email called');
        
        const body = await request.json();
        console.log('API: Request body:', body);
        
        const { email } = body;

        if (!email || typeof email !== 'string') {
            console.log('API: Invalid email provided');
            return NextResponse.json(
                { error: 'Email is required' },
                { status: 400 }
            );
        }

        console.log('API: Checking email existence for:', email);
        const userExists = await checkUserExistsByEmail(email);
        console.log('API: Email exists result:', userExists);

        return NextResponse.json({ exists: userExists });
    } catch (error) {
        console.error('API: Error checking email:', error);
        return NextResponse.json(
            { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
            { status: 500 }
        );
    }
}
