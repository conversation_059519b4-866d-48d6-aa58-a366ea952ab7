import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

/**
 * OAuth API Route for Google Sign-In
 * 
 * After successful Google OAuth authentication, users will be redirected to the home page (/).
 * 
 * Required environment variables:
 * - SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID: Google OAuth Client ID
 * - SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_SECRET: Google OAuth Client Secret
 * 
 * Setup steps:
 * 1. Configure Google OAuth in Google Cloud Console
 * 2. Add environment variables to your .env file
 * 3. Restart your development server
 */

export async function POST(request: NextRequest) {
  try {
    const { provider, next } = await request.json();

    if (!provider) {
      return NextResponse.json(
        { error: 'Provider is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    const getURL = () => {
      let url =
        process?.env?.NEXT_PUBLIC_SITE_URL ?? // Set this to your site URL in production env.
        process?.env?.NEXT_PUBLIC_VERCEL_URL ?? // Automatically set by Vercel.
        'http://localhost:3000/'
      // Make sure to include `https://` when not localhost.
      url = url.startsWith('http') ? url : `https://${url}`
      // Make sure to include a trailing `/`.
      url = url.endsWith('/') ? url : `${url}/`
      return url
    }


    // Build redirect URL properly
    const baseUrl = getURL();
    const callbackUrl = new URL('auth/callback', baseUrl);
    console.log('callbackUrl', callbackUrl);
    // Add next parameter if provided
    if (next) {
      callbackUrl.searchParams.set('next', next);
    }

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider as 'google' | 'github' | 'facebook',
      options: {
        redirectTo: callbackUrl.toString(),
      }
    });

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      url: data.url,
    });
  } catch (error) {
    console.error('OAuth API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
