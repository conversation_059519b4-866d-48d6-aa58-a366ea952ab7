import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function Page() {
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-md">
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader className="text-center pb-6">
              {/* Success Icon */}
              <div className="mx-auto mb-6 flex h-24 w-24 items-center justify-center">
                <Image
                  src="/sign-up-success-icon.png"
                  alt="Success"
                  width={96}
                  height={96}
                  className="h-full w-full object-contain"
                  priority
                />
              </div>
              <CardTitle className="text-2xl">
                Cảm ơn bạn đã đăng ký tài khoản PawHub.
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-6 px-8 pb-8">
              <p className="text-base text-muted-foreground text-center leading-relaxed">
                Vui lòng kiểm tra email bạn sử dụng đăng ký và bấm vào link xác nhận để hoàn tất việc tạo tài khoản.
              </p>
              <Button asChild className="w-full h-12 text-base">
                <Link href="/" className="bg-yellow-500 hover:bg-yellow-400">Về trang chủ</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
