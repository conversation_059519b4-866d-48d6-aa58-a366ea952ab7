import type { Metadata } from "next";
import { AuthHeader } from "@/components/auth-header";

export const metadata: Metadata = {
  title: "Authentication - PawHub",
  description: "Sign in or create your PawHub account",
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="min-h-screen relative">
      <AuthHeader />
      <main className="min-h-screen pt-16 sm:pt-20">
        {children}
      </main>
    </div>
  );
}
